<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create admin user with specified credentials
        User::create([
            'name' => 'Munyathatguy',
            'email' => '<EMAIL>',
            'password' => Hash::make('p+s]I%2c@=xn'),
        ]);
    }
}
