interface Theme {
  "color-scheme": string
  "--color-base-100": string
  "--color-base-200": string
  "--color-base-300": string
  "--color-base-content": string
  "--color-primary": string
  "--color-primary-content": string
  "--color-secondary": string
  "--color-secondary-content": string
  "--color-accent": string
  "--color-accent-content": string
  "--color-neutral": string
  "--color-neutral-content": string
  "--color-info": string
  "--color-info-content": string
  "--color-success": string
  "--color-success-content": string
  "--color-warning": string
  "--color-warning-content": string
  "--color-error": string
  "--color-error-content": string
  "--radius-selector": string
  "--radius-field": string
  "--radius-box": string
  "--size-selector": string
  "--size-field": string
  "--border": string
  "--depth": string
  "--noise": string
}


interface Themes {
  black: Theme
  halloween: Theme
  cyberpunk: Theme
  cupcake: Theme
  fantasy: Theme
  wireframe: Theme
  cmyk: Theme
  nord: Theme
  emerald: Theme
  abyss: Theme
  lofi: Theme
  night: Theme
  aqua: Theme
  pastel: Theme
  forest: Theme
  dark: Theme
  lemonade: Theme
  valentine: Theme
  retro: Theme
  winter: Theme
  coffee: Theme
  light: Theme
  silk: Theme
  dim: Theme
  autumn: Theme
  caramellatte: Theme
  sunset: Theme
  luxury: Theme
  business: Theme
  acid: Theme
  synthwave: Theme
  corporate: Theme
  dracula: Theme
  garden: Theme
  bumblebee: Theme
  [key: string]: Theme
}

declare const themes: Themes
export default themes