<?php $__env->startSection('content'); ?>
    <!-- Hero Section -->
    <section class="hero bg-gray-100 py-16 text-center">
        <div class="container mx-auto px-4">
            <h1 class="text-4xl font-bold text-gray-800 mb-4">Welcome to South Safari</h1>
            <p class="text-xl text-gray-600 max-w-2xl mx-auto">Your gateway to digital adventures in Southern Africa. We connect developers with unique opportunities and create impactful digital experiences.</p>
        </div>
    </section>

    <!-- About Section -->
    <section class="about py-16">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                <h2 class="text-3xl font-bold text-gray-800 mb-6 text-center">About South Safari</h2>
                <div class="grid md:grid-cols-2 gap-8">
                    <div>
                        <p class="text-gray-600 mb-4">South Safari is a platform connecting developers with unique opportunities in Southern Africa. We believe in innovation, collaboration, and creating impactful digital experiences that make a difference.</p>
                        <p class="text-gray-600">Our mission is to bridge the gap between technology and opportunity, fostering growth and development across the region.</p>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold text-gray-800 mb-3">Our Values</h3>
                        <ul class="text-gray-600 space-y-2">
                            <li>• Innovation and creativity</li>
                            <li>• Collaboration and partnership</li>
                            <li>• Quality and excellence</li>
                            <li>• Community impact</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section class="services py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto text-center">
                <h2 class="text-3xl font-bold text-gray-800 mb-6">Our Services</h2>
                <div class="grid md:grid-cols-3 gap-8">
                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="text-xl font-semibold text-gray-800 mb-3">Digital Product Deployment</h3>
                        <p class="text-gray-600">We help bring your digital products to market with expert deployment and optimization strategies.</p>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="text-xl font-semibold text-gray-800 mb-3">Market Expansion</h3>
                        <p class="text-gray-600">Expand your reach across Southern Africa with our local market knowledge and expertise.</p>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="text-xl font-semibold text-gray-800 mb-3">Developer Partnerships</h3>
                        <p class="text-gray-600">Connect with talented developers and build lasting partnerships for your projects.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact py-16">
        <div class="container mx-auto px-4">
            <div class="max-w-2xl mx-auto">
                <h2 class="text-3xl font-bold text-gray-800 mb-6 text-center">Get In Touch</h2>
                
                <?php if(session('success')): ?>
                    <div class="alert alert-success mb-6 p-4 bg-green-100 border border-green-400 text-green-700 rounded">
                        <?php echo e(session('success')); ?>

                    </div>
                <?php endif; ?>

                <?php if($errors->any()): ?>
                    <div class="alert alert-error mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
                        <ul class="list-disc list-inside">
                            <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li><?php echo e($error); ?></li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <form action="<?php echo e(route('contact.submit')); ?>" method="POST" class="space-y-6">
                    <?php echo csrf_field(); ?>
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Name</label>
                        <input type="text" 
                               id="name" 
                               name="name" 
                               value="<?php echo e(old('name')); ?>"
                               required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                    
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                        <input type="email" 
                               id="email" 
                               name="email" 
                               value="<?php echo e(old('email')); ?>"
                               required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                    
                    <div>
                        <label for="message" class="block text-sm font-medium text-gray-700 mb-2">Message</label>
                        <textarea id="message" 
                                  name="message" 
                                  rows="5" 
                                  required 
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"><?php echo e(old('message')); ?></textarea>
                    </div>
                    
                    <button type="submit" 
                            class="w-full bg-primary text-white py-2 px-4 rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition duration-200">
                        Send Message
                    </button>
                </form>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\southsafari\resources\views/home.blade.php ENDPATH**/ ?>